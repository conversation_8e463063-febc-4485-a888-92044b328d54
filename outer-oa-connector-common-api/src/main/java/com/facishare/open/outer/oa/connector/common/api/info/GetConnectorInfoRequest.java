package com.facishare.open.outer.oa.connector.web.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 获取oa类连接器绑定信息请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetConnectorInfoRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * oa类渠道
     * 渠道名称：feishu飞书连接器标识，lark接器标识
     */
    private String channel;
    
    /**
     * 纷享企业ea
     */
    private String fsEa;
}
