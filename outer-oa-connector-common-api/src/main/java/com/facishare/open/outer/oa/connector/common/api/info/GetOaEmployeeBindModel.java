package com.facishare.open.outer.oa.connector.common.api.info;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 获取oa类连接器绑定信息请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetOaConnectorInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * oa类渠道
     */
    private ChannelEnum channel;
    
    /**
     * 纷享企业ea
     */
    private String fsEa;

    /**
     * 外部企业id
     */
    private String outEa;

    /**
     * 绑定的应用id
     */
    private String appId;
}
